#!/usr/bin/env python3
"""
Test script to check vLLM configuration loading.
"""

import asyncio
import logging
from mcp_agent.app import MCPApp

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_config():
    """Test configuration loading"""
    
    logger.info("=== Testing Configuration Loading ===")
    
    # Initialize app
    app = MCPApp(name="config_test", human_input_callback=None)
    
    async with app.run() as test_app:
        context = test_app.context
        logger.info("✓ MCP App initialized successfully")
        
        # Check configuration
        logger.info(f"Context config: {context.config}")
        logger.info(f"Has vLLM config: {hasattr(context.config, 'vllm')}")
        
        if hasattr(context.config, 'vllm') and context.config.vllm:
            vllm_config = context.config.vllm
            logger.info(f"vLLM config: {vllm_config}")
            logger.info(f"vLLM api_base: {vllm_config.api_base}")
            logger.info(f"vLLM default_model: {vllm_config.default_model}")
            logger.info(f"vLLM api_key: {vllm_config.api_key}")
        else:
            logger.error("No vLLM configuration found!")
            
        # Check OpenAI config for comparison
        if hasattr(context.config, 'openai') and context.config.openai:
            openai_config = context.config.openai
            logger.info(f"OpenAI config: {openai_config}")
            logger.info(f"OpenAI default_model: {openai_config.default_model}")
        else:
            logger.info("No OpenAI configuration found")

if __name__ == "__main__":
    asyncio.run(test_config())
