#!/usr/bin/env python3
"""
Simple test script to verify vLLM integration is working correctly.
"""

import asyncio
import logging
from mcp_agent.app import MCPA<PERSON>
from mcp_agent.workflows.llm.augmented_llm_vllm import VLLMAugmentedLLM
from mcp_agent.agents.agent import Agent

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_vllm_integration():
    """Test vLLM integration directly"""
    
    logger.info("=== Testing vLLM Integration ===")
    
    # Initialize app
    app = MCPApp(name="vllm_test", human_input_callback=None)
    
    async with app.run() as test_app:
        context = test_app.context
        logger.info("✓ MCP App initialized successfully")
        
        # Create a simple agent
        agent = Agent(
            name="test_agent",
            instruction="You are a test agent.",
            server_names=[]
        )
        
        # Initialize agent with context
        agent.context = context
        await agent.initialize()
        logger.info("✓ Agent initialized")

        # Check if agent already has an LLM
        logger.info(f"Agent LLM before attach: {agent.llm}")
        logger.info(f"Agent LLM type before attach: {type(agent.llm) if agent.llm else None}")

        # Attach vLLM
        llm = await agent.attach_llm(VLLMAugmentedLLM)
        logger.info(f"✓ vLLM attached: {type(llm).__name__}")
        logger.info(f"✓ Provider: {llm.provider}")
        logger.info(f"✓ API Base: {llm.vllm_api_base}")
        
        # Check default request params
        if llm.default_request_params:
            logger.info(f"✓ Default model: {llm.default_request_params.model}")
        else:
            logger.warning("✗ No default request params set")
        
        # Test model selection
        try:
            selected_model = await llm.select_model()
            logger.info(f"✓ Selected model: {selected_model}")
        except Exception as e:
            logger.error(f"✗ Model selection failed: {e}")
        
        # Test simple generation (this will fail if vLLM server is not running, but we can see the model being used)
        try:
            logger.info("Testing simple generation...")
            result = await llm.generate_str("Hello, this is a test message.")
            logger.info(f"✓ Generation successful: {result[:100]}...")
        except Exception as e:
            logger.info(f"Generation failed (expected if vLLM server not running): {e}")
            # Check if the error mentions the correct model
            if "Qwen" in str(e):
                logger.info("✓ Error mentions Qwen model - integration working!")
            elif "gpt-4o-mini" in str(e):
                logger.error("✗ Error mentions gpt-4o-mini - integration not working!")
            else:
                logger.info(f"Error doesn't mention specific model: {e}")

if __name__ == "__main__":
    asyncio.run(test_vllm_integration())
